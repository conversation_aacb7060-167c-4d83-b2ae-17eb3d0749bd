'use client'

import { useEffect, useRef, useState } from 'react'
import { useQueue } from '@/hooks/useQueue'
import { YouTubePlayer } from '@/lib/types/video'
import { formatTime } from '@/lib/utils/time'

declare global {
  interface Window {
    YT: any
    onYouTubeIframeAPIReady: () => void
    youtubePlayer: YouTubePlayer | null
  }
}

export function VideoPlayer() {
  const {
    currentVideo,
    isPlaying,
    nextVideo,
    setPlaying,
    items,
    currentIndex,
    isLooping
  } = useQueue()
  const playerRef = useRef<HTMLDivElement>(null)
  const [player, setPlayer] = useState<YouTubePlayer | null>(null)
  const [isPlayerReady, setIsPlayerReady] = useState(false)
  const [isPlayerInstanceReady, setIsPlayerInstanceReady] = useState(false)
  const timeMonitorRef = useRef<NodeJS.Timeout | null>(null)
  const videoLoopCountRef = useRef<{ [videoId: string]: number }>({})
  const lastLoadedVideoId = useRef<string | null>(null)
  const hasScheduledSeek = useRef<boolean>(false)
  const isLoadingVideo = useRef<boolean>(false)

  // Initialize YouTube API
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.YT) {
      console.log('📺 Loading YouTube IFrame API...')

      // Set up the callback for when API is ready
      window.onYouTubeIframeAPIReady = () => {
        console.log('✅ YouTube IFrame API ready')
        setIsPlayerReady(true)
      }

      // Load the API
      const tag = document.createElement('script')
      tag.src = 'https://www.youtube.com/iframe_api'
      const firstScriptTag = document.getElementsByTagName('script')[0]
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)
    } else if (window.YT) {
      setIsPlayerReady(true)
    }
  }, [])

  // Create player when API is ready and we have a video
  useEffect(() => {
    if (isPlayerReady && currentVideo && playerRef.current && !player) {
      console.log('🎬 [CREATE PLAYER] Creating YouTube player for:', currentVideo.title, 'ID:', currentVideo.id)

      // Mark this video as already loaded to prevent duplicate loading in VIDEO CHANGE EFFECT
      lastLoadedVideoId.current = currentVideo.id
      // Reset loading flag since we're creating a new player
      isLoadingVideo.current = false

      const newPlayer = new window.YT.Player(playerRef.current, {
        height: '100%',
        width: '100%',
        videoId: currentVideo.id,
        playerVars: {
          autoplay: 1,
          controls: 1,
          rel: 0,
          modestbranding: 1,
          fs: 1,
          cc_load_policy: 0,
          iv_load_policy: 3,
          autohide: 0,
          start: currentVideo.startTime || 0  // Start at the specified time
        },
        events: {
          onReady: (event: any) => {
            console.log('🎬 [onReady] YouTube player ready for video:', currentVideo?.id)
            setIsPlayerInstanceReady(true)
            // Clear loading flag when player is ready
            isLoadingVideo.current = false

            console.log('🎬 [onReady] Starting playback for video:', currentVideo?.id)
            event.target.playVideo()
            setPlaying(true)

            console.log('🎬 [onReady] Video should start at:', currentVideo?.startTime || 0, 'seconds')

            // Start time monitoring if video has end time
            if (currentVideo?.endTime) {
              console.log('🎬 [onReady] Starting time monitoring for video:', currentVideo?.id)
              setTimeout(() => startTimeMonitoring(event.target), 1500)
            }
          },
          onStateChange: (event: any) => {
            const state = event.data
            console.log('🎵 [onStateChange] Player state changed:', state, 'for video:', currentVideo?.id)

            // YouTube player states:
            // -1 (unstarted), 0 (ended), 1 (playing), 2 (paused), 3 (buffering), 5 (cued)

            if (state === 1) { // Playing
              setPlaying(true)

              // Fallback: If video just started playing and we have a start time,
              // check if we need to seek (but only if no seek is already scheduled)
              if (currentVideo?.startTime && lastLoadedVideoId.current === currentVideo.id && !hasScheduledSeek.current) {
                const currentTime = event.target.getCurrentTime()
                const startTime = currentVideo.startTime
                // If we're not close to the start time (tolerance of 2 seconds), seek to it
                if (Math.abs(currentTime - startTime) > 2) {
                  console.log(`⏰ Fallback seeking from ${currentTime}s to ${startTime}s for video ${currentVideo.id}`)
                  hasScheduledSeek.current = true
                  event.target.seekTo(startTime, true)
                }
              }
            } else if (state === 2) { // Paused
              setPlaying(false)
            } else if (state === 0) { // Ended
              setPlaying(false)
              handleVideoEnded()
            }
          },
          onError: (event: any) => {
            console.error('❌ YouTube player error:', event.data)
            // Try to skip to next video on error
            handleVideoEnded()
          }
        }
      })

      setPlayer(newPlayer)
      window.youtubePlayer = newPlayer
    }
  }, [isPlayerReady, currentVideo, player])

  // Reset player instance ready state when player changes
  useEffect(() => {
    if (!player) {
      setIsPlayerInstanceReady(false)
    }
  }, [player])

  // Handle video ended - move to next video or handle looping
  const handleVideoEnded = (playerInstance?: any) => {
    console.log('🔄 Video ended')
    const activePlayer = playerInstance || player

    // Stop time monitoring
    if (timeMonitorRef.current) {
      clearInterval(timeMonitorRef.current)
      timeMonitorRef.current = null
    }

    if (items.length > 0) {
      const currentItem = items[currentIndex]
      const videoId = currentItem.id

      // Get current loop count for this video (default to 0)
      const currentLoops = videoLoopCountRef.current[videoId] || 0
      const targetLoops = currentItem.loopCount || 1

      console.log(`🔍 Video ${videoId}: completed ${currentLoops + 1}/${targetLoops} loops`)

      if (currentLoops + 1 < targetLoops) {
        // Loop this video again
        videoLoopCountRef.current[videoId] = currentLoops + 1
        console.log(`🔁 Looping video again: ${currentLoops + 1}/${targetLoops}`)

        // Simply seek to start time if it exists, otherwise restart from beginning
        console.log(`🔁 Attempting to seek - activePlayer available:`, !!activePlayer)
        if (activePlayer) {
          const seekTime = currentItem.startTime || 0
          console.log(`🔁 Seeking to time: ${seekTime}s`)
          activePlayer.seekTo(seekTime, true)

          // Restart monitoring if needed
          if (currentItem.endTime) {
            console.log(`🔁 Restarting monitoring for endTime: ${currentItem.endTime}s`)
            setTimeout(() => startTimeMonitoring(activePlayer), 500)
          }
        } else {
          console.error('❌ Player not available for seeking!')
        }
        return
      }

      // Video has completed all its loops, move to next
      console.log(`✅ Video completed all ${targetLoops} loops, moving to next`)
      console.log(`📊 Current state: currentIndex=${currentIndex}, items.length=${items.length}, isLooping=${isLooping}`)

      const nextIndex = currentIndex + 1
      if (nextIndex < items.length) {
        console.log(`➡️ Moving to next video at index ${nextIndex}`)
        nextVideo()
      } else if (isLooping) {
        console.log(`🔄 End of queue reached, looping back to start`)
        nextVideo() // Queue loop
      } else {
        setPlaying(false)
        console.log('🏁 Queue finished')
      }
    }
  }

  // Start time monitoring for videos with end times
  const startTimeMonitoring = (playerInstance?: any) => {
    const activePlayer = playerInstance || player

    if (!currentVideo?.endTime || !activePlayer) {
      console.log('⏰ [MONITOR] Not starting monitoring - endTime:', currentVideo?.endTime, 'player:', !!activePlayer)
      return
    }

    console.log(`⏰ [MONITOR] Starting time monitoring for video ${currentVideo.id} - endTime: ${currentVideo.endTime}s`)

    // Clear any existing monitor
    if (timeMonitorRef.current) {
      clearInterval(timeMonitorRef.current)
    }

    // Monitor playback time every 500ms
    timeMonitorRef.current = setInterval(() => {
      try {
        if (activePlayer && typeof activePlayer.getCurrentTime === 'function') {
          const currentTime = activePlayer.getCurrentTime()
          const endTime = currentVideo.endTime!

          // Check if currentTime is valid before using it
          if (typeof currentTime === 'number' && !isNaN(currentTime)) {
            console.log(`⏰ [MONITOR] Current time: ${currentTime.toFixed(1)}s / End time: ${endTime}s`)

            // Check if we've reached the end time (with 0.5s tolerance)
            if (currentTime >= endTime - 0.5) {
            console.log(`⏰ [MONITOR] Reached end time ${endTime}s at ${currentTime}s`)

            // Stop monitoring
            if (timeMonitorRef.current) {
              clearInterval(timeMonitorRef.current)
              timeMonitorRef.current = null
            }

            // Handle as if video ended
            handleVideoEnded(activePlayer)
            }
          }
        }
      } catch (error) {
        console.error('❌ Error monitoring video time:', error)
      }
    }, 500)
  }

  // Reset loop counters when queue items change (new queue loaded)
  useEffect(() => {
    console.log('🔄 Queue items changed (new queue loaded), resetting all loop counters. Items length:', items.length)
    videoLoopCountRef.current = {}
  }, [items])



  // Update player when current video changes (but not on initial load)
  useEffect(() => {
    console.log('🔄 [VIDEO CHANGE EFFECT] Triggered - player:', !!player, 'ready:', isPlayerInstanceReady, 'currentVideo:', currentVideo?.id, 'isLoading:', isLoadingVideo.current)

    // Skip if this is the initial video load (player was just created with this video)
    // or if we're already loading a video
    if (player && isPlayerInstanceReady && lastLoadedVideoId.current !== currentVideo?.id && !isLoadingVideo.current) {
      if (currentVideo && currentVideo.id) {
        console.log('🔄 [VIDEO CHANGE EFFECT] Loading new video:', currentVideo.title, 'ID:', currentVideo.id)

        // Set loading flag to prevent duplicate loads
        isLoadingVideo.current = true

        // Reset loop count for this video when switching to it (fresh start for each video)
        videoLoopCountRef.current[currentVideo.id] = 0
        console.log(`🔄 Reset loop counter for ${currentVideo.id}: 0`)

        try {
          console.log(`🔄 [VIDEO CHANGE EFFECT] Loading video ${currentVideo.id} with startTime: ${currentVideo.startTime || 0}s`)

          // Track which video we're loading
          lastLoadedVideoId.current = currentVideo.id

          // Reset seek flag for new video
          hasScheduledSeek.current = false

          // Always load video normally first, then seek to ensure proper start time
          console.log(`🔄 [VIDEO CHANGE EFFECT] Calling player.loadVideoById for: ${currentVideo.id}`)
          player.loadVideoById(currentVideo.id)

          // If video has start time, schedule a seek after loading
          if (currentVideo.startTime) {
            hasScheduledSeek.current = true
            // Use a longer timeout to ensure video is loaded before seeking
            setTimeout(() => {
              if (player && typeof player.seekTo === 'function' && lastLoadedVideoId.current === currentVideo.id) {
                console.log(`🔄 [VIDEO CHANGE EFFECT] Seeking to start time: ${currentVideo.startTime}s for video ${currentVideo.id}`)
                player.seekTo(currentVideo.startTime, true)
              }
            }, 1500)
          }

          // Start time monitoring if video has end time
          if (currentVideo.endTime) {
            setTimeout(() => startTimeMonitoring(player), 2000)
          }

          // Clear loading flag after a delay to allow the video to start loading
          setTimeout(() => {
            isLoadingVideo.current = false
          }, 2000)
        } catch (error) {
          console.error('❌ Error loading video:', error)
          isLoadingVideo.current = false
        }
      } else {
        // No current video - destroy player
        console.log('🛑 No current video, destroying player')
        try {
          player.destroy()
          setPlayer(null)
          setIsPlayerInstanceReady(false)
          window.youtubePlayer = null
          if (timeMonitorRef.current) {
            clearInterval(timeMonitorRef.current)
            timeMonitorRef.current = null
          }
        } catch (error) {
          console.error('❌ Error destroying player:', error)
        }
      }
    }
  }, [player, isPlayerInstanceReady, currentVideo])

  // Control player based on isPlaying state
  useEffect(() => {
    // Don't try to control player if there's no current video (queue cleared)
    if (!currentVideo || !player || !isPlayerInstanceReady) {
      return
    }

    // Additional safety check to ensure player methods exist
    if (typeof player.getPlayerState !== 'function' ||
        typeof player.playVideo !== 'function' ||
        typeof player.pauseVideo !== 'function') {
      return
    }

    try {
      const playerState = player.getPlayerState()

      if (isPlaying && playerState !== 1) { // Not playing
        player.playVideo()
      } else if (!isPlaying && playerState === 1) { // Currently playing
        player.pauseVideo()
      }
    } catch (error) {
      console.error('❌ Error controlling player:', error)
    }
  }, [player, isPlayerInstanceReady, isPlaying, currentVideo])

  // Cleanup time monitoring on unmount
  useEffect(() => {
    return () => {
      if (timeMonitorRef.current) {
        clearInterval(timeMonitorRef.current)
        timeMonitorRef.current = null
      }
    }
  }, [])

  return (
    <div className="glassmorphism rounded-2xl overflow-hidden">
      <div className="aspect-video bg-black relative">
        {currentVideo ? (
          <div
            ref={playerRef}
            className="w-full h-full"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-dark-400">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
              <p className="text-lg font-medium">No video selected</p>
              <p className="text-sm">Add videos to your queue to start playing</p>
            </div>
          </div>
        )}
      </div>

      {currentVideo && (
        <div className="p-4 border-t border-white/10">
          <h3 className="font-medium text-white truncate mb-1">
            {currentVideo.title}
          </h3>
          <p className="text-sm text-dark-300 truncate mb-2">
            {currentVideo.channel || 'Unknown Channel'}
          </p>

          {/* Loop and timeframe information */}
          {(currentVideo.startTime || currentVideo.endTime || (currentVideo.loopCount && currentVideo.loopCount > 1)) && (
            <div className="flex flex-wrap gap-2 text-xs">
              {/* Timeframe info */}
              {(currentVideo.startTime || currentVideo.endTime) && (
                <div className="bg-primary-600/20 text-primary-300 px-2 py-1 rounded-md flex items-center gap-1">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  <span>
                    {currentVideo.startTime ? formatTime(currentVideo.startTime) : '0:00'}
                    {' - '}
                    {currentVideo.endTime ? formatTime(currentVideo.endTime) : 'end'}
                  </span>
                </div>
              )}

              {/* Loop count info */}
              {currentVideo.loopCount && currentVideo.loopCount > 1 && (
                <div className="bg-accent-600/20 text-accent-300 px-2 py-1 rounded-md flex items-center gap-1">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
                  </svg>
                  <span>{(videoLoopCountRef.current[currentVideo.id] || 0) + 1}/{currentVideo.loopCount}</span>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
